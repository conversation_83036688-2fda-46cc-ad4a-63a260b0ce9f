import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import cluster from 'cluster';
import os from 'os';
import configurations from './configurations';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import bodyParser from 'body-parser';
import { delay } from './util/delay.util';

async function startApp() {
  const app = await NestFactory.create(AppModule);

  // Use Winston for logging
  app.useLogger(app.get(WINSTON_MODULE_NEST_PROVIDER));
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });

  app.use(bodyParser.json({ limit: '10mb' }));
  app.use(bodyParser.urlencoded({ limit: '10mb', extended: true }));

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  );

  const swaggerConfig = new DocumentBuilder()
    .setTitle(configurations('APP_NAME'))
    .setVersion('1.0')
    .addBearerAuth({
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'API Key',
      description: 'Enter your API key as Bearer token',
    })
    .build();

  if (configurations('NODE_ENV') === 'development') {
    const document = SwaggerModule.createDocument(app, swaggerConfig);
    SwaggerModule.setup('', app, document);
  }

  const port = configurations('PORT') || 3000;
  await app.listen(port);
  console.log(`Application is running on port ${port}`);
}

async function startCluster() {
  const allocatedCore = configurations('CPU_ALLOCATION')
    ? Math.min(os.cpus().length, configurations('CPU_ALLOCATION'))
    : os.cpus().length;

  if (cluster.isPrimary) {
    console.log(`Cluster started with ${allocatedCore} worker(s)`);

    for (let i = 0; i < allocatedCore; i++) {
      cluster.fork({
        CORE_NUMBER: i,
      });
    }

    cluster.on('exit', (worker) => {
      console.log(`Worker with ID ${worker.process.pid} has exited.`);
      console.log('Creating a new worker...');
      const CORE_NUMBER = process.env.CORE_NUMBER;
      cluster.fork({
        CORE_NUMBER,
      });
    });
  } else {
    let coreNumber = 0;
    const workerId = cluster.worker?.id || 0;
    for (let i = -(workerId + 1); i < allocatedCore; i += allocatedCore) {
      coreNumber = i;
    }
    console.log(`Worker ${coreNumber} with ID ${process.pid} has started.`);
    await delay(Math.round(1000 / allocatedCore));
    await startApp();
  }
}

if (configurations('DISABLE_CLUSTER') === 'true') {
  startApp();
} else {
  startCluster();
}
