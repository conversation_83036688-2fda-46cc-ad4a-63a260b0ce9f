import * as fs from 'fs';
import * as dotenv from 'dotenv';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import * as os from 'os';
import { CandlestickPatternService } from './services/candlestick-pattern.service';
import { CandlestickService } from './services/candlestick.service';

const NODE_ENV = process.env.NODE_ENV || 'development';
const envFilePath = NODE_ENV === 'development' ? '../.env.development' : `.env`;
const allocatedCore = os.cpus().length;

const patternService = new CandlestickPatternService(new CandlestickService());

// Ambil semua property dari prototype (termasuk method)
const CANDLESTICK_PATTERNS = Object.getOwnPropertyNames(
  Object.getPrototypeOf(patternService),
).filter((name) => {
  const prop = (patternService as any)[name];
  return typeof prop === 'function' && name !== 'constructor';
});

export default (key: string) => {
  const env = dotenv.parse(fs.readFileSync(envFilePath));
  const keys = {
    ...env,
    APP_NAME: 'PantauFX Backtest API',
    UUID_NAMESPACE: '3d594650-3436-4a5b-a3da-10fd8e4eb4c5',
    PORT: process.env.PORT || 3001,
    NODE_ENV,
    TRADE_EXECUTOR_ENABLED: env.TRADE_EXECUTOR_ENABLED === 'true',
    ADAPTER_EXTERNAL_ENABLED: env.ADAPTER_EXTERNAL_ENABLED === 'true',
    ADAPTER_EXTERNAL_URL: env.ADAPTER_EXTERNAL_URL,
    ADAPTER_EXTERNAL_API_KEY: env.ADAPTER_EXTERNAL_API_KEY,
    LOG_LEVEL: env.LOG_LEVEL || 'error',
    LOG_DIR: env.LOG_DIR || 'logs',
    TOTAL_SERVER: Number(env.TOTAL_SERVER),
    SERVER_NUMBER: Number(env.SERVER_NUMBER),
    CPU_ALLOCATION: Number(env.CPU_ALLOCATION) || undefined,
    DEFAULT_LIMIT: Number(env.DEFAULT_LIMIT) || 100,
    HISTORICAL_EXECUTION_LIMIT:
      Number(env.HISTORICAL_EXECUTION_LIMIT) || 1000000000000,
    TOTAL_CORE: allocatedCore,
    CORE_NUMBER: Number(process.env.CORE_NUMBER),
    DEFAULT_LOOKBACK_PERIOD: Number(env.DEFAULT_LOOKBACK_PERIOD) || 1000,
    OPTIMUM_EQUITY_ALLOCATION: Number(env.OPTIMUM_EQUITY_ALLOCATION) || 0.6,
    INTERVALS: env.INTERVALS.split(',').map(String),
    SYMBOLS: (env.SYMBOLS || 'BTCUSDT').split(','),
    METHOD_MIN_PROBABILITY: Number(env.METHOD_MIN_PROBABILITY) || 70,
    METHOD_MIN_VALID_TRADE: Number(env.METHOD_MIN_VALID_TRADE) || 10,
    METHOD_MAX_CONSECUTIVE_LOSS: Number(env.METHOD_MAX_CONSECUTIVE_LOSS) || 3,
    ESTIMATED_MAX_DRAWDOWN: Number(env.ESTIMATED_MAX_DRAWDOWN) || 18,
    METHOD_MAX_OPEN_POSITIONS: Number(env.METHOD_MAX_OPEN_POSITIONS) || 1,
    METHOD_TYPE: (env.METHOD_TYPE || 'static,dynamic').split(','),
    TRADE_MIN_PROBABILITY: Number(env.TRADE_MIN_PROBABILITY) || 70,
    SYMBOL_LAUNCH_TIME_THRESHOLD_DAYS:
      Number(env.SYMBOL_LAUNCH_TIME_THRESHOLD_DAYS) || 730,
    METHOD_REWARD_PERCENT_SAMPLES: (
      env.METHOD_REWARD_PERCENT_SAMPLES || '1,2,3'
    )
      .split(',')
      .map(Number),
    EXECUTION_INTERVAL: env.EXECUTION_INTERVAL || '15',
    METHOD_MIN_PROBABILITIES: (env.METHOD_MIN_PROBABILITIES || '70')
      .split(',')
      .map(Number),
    METHOD_MAX_DRAWDOWNS: (env.METHOD_MAX_DRAWDOWNS || '0.9')
      .split(',')
      .map(Number),
    TRENDS: (env.TRENDS || 'up,down,neutral').split(','),
    ORDER_TYPES: (env.ORDER_TYPES || 'long,short').split(','),
    METHOD_LIMIT: Number(env.METHOD_LIMIT) || 1000,
    METHOD_SIGNAL_LOOKBACK_PERIOD: (
      env.METHOD_SIGNAL_LOOKBACK_PERIOD || '100,200,300'
    )
      .split(',')
      .map(Number),
    METHOD_SIGNAL_VALIDITY_PERIOD: (
      env.METHOD_SIGNAL_VALIDITY_PERIOD || '100,200,300'
    )
      .split(',')
      .map(Number),
    METHOD_ENTRY_PERCENT_BY_CLOSE_SAMPLES: (
      env.METHOD_ENTRY_PERCENT_BY_CLOSE_SAMPLES || '1,2,3'
    )
      .split(',')
      .map((item) => Math.abs(Number(item))),
    METHOD_RISK_PERCENT_SAMPLES: (env.METHOD_RISK_PERCENT_SAMPLES || '1,2,3')
      .split(',')
      .map((item) => Math.abs(Number(item))),
    METHOD_REWARD_RATIO_SAMPLES: (env.METHOD_REWARD_RATIO_SAMPLES || '1,2,3')
      .split(',')
      .map((item) => Math.abs(Number(item))),
    CANDLESTICK_PATTERNS,
    PATTERN_TYPES: (env.PATTERN_TYPES || 'candlestick').split(','),
    DATABASE: {
      type: env.DB_TYPE,
      host: env.DB_HOST,
      port: env.DB_PORT,
      username: env.DB_USER,
      password: env.DB_PASSWORD,
      database: env.DB_NAME,
      entities: [__dirname + '/**/*.entity{.ts,.js}'],
      synchronize: process.env.NODE_ENV !== 'production',
      ssl:
        env.DB_SSL_ENABLED === 'true'
          ? {
              rejectUnauthorized: false,
              ca: fs.readFileSync('sql-ca.crt', 'utf8'),
            }
          : false,
    } as TypeOrmModuleOptions,
  };
  return keys[key];
};
