import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import configurations from 'src/configurations';
import { HistoricalIngestionService } from './historical-ingestion.service';
import { InstrumentIngestionService } from './instrument-ingestion.service';
import { MethodStatusService } from './method-status.service';
import { MethodUpdaterService } from './method-updater.service';
import { MethodIngestionService } from './method-ingestion.service';
import { InstrumentService } from './instrument.service';
import { shuffleArray } from 'src/util/suffle-array.util';
import { getSymbolsSlice } from 'src/util/get-symbols-slice.util';
import { TraderExecutorService } from './trade-executor.service';
import { TradeResultUpdaterService } from './trade-result-updater.service';

@Injectable()
export class TaskService implements OnModuleInit {
  private readonly logger = new Logger(TaskService.name);
  private isEngineRunning = false;

  constructor(
    private readonly historicalIngestionService: HistoricalIngestionService,
    private readonly methodStatusService: MethodStatusService,
    private readonly instrumentIngestionService: InstrumentIngestionService,
    private readonly methodUpdaterService: MethodUpdaterService,
    private readonly methodIngestionService: MethodIngestionService,
    private readonly instrumentService: InstrumentService,
    private readonly traderExecutorService: TraderExecutorService,
    private readonly tradeResultUpdaterService: TradeResultUpdaterService,
  ) {}

  async onModuleInit() {
    const engineMode = configurations('ENGINE_MODE');
    const tradeExecutorEnabled = configurations('TRADE_EXECUTOR_ENABLED');
    if (engineMode === 'adapter') return;
    if (engineMode === 'worker') {
      this.isEngineRunning = true;
      await this.instrumentIngestionService.ingest(true);
      await this.historicalIngestionService.ingestHistorical();

      const disableCluster = configurations('DISABLE_CLUSTER');
      let symbols = await this.instrumentService.getSymbols();

      symbols =
        disableCluster === 'false'
          ? shuffleArray(getSymbolsSlice(symbols))
          : shuffleArray(symbols);
      this.logger.log('Method ingestion loop triggered.');

      await this.methodIngestionService.ingestForever(symbols);
    }
    if (engineMode === 'service') {
      (async () => {
        this.isEngineRunning = true;
        const now = new Date();
        await this.instrumentIngestionService.ingest(true);
        await this.historicalIngestionService.ingestHistorical();
        await this.methodStatusService.resetMethodStatusByRunningResult();
        await this.methodUpdaterService.updateMethod(now);
        await this.tradeResultUpdaterService.updateTradeResult();
        if (tradeExecutorEnabled) await this.traderExecutorService.run();
        this.isEngineRunning = false;
      })();
    }
  }

  @Cron('0 */4 * * *')
  async handleInstrumentStatus() {
    const engineMode = configurations('ENGINE_MODE');
    if (engineMode === 'adapter') return;
    this.logger.log('Instrument ingestion job triggered.');
    await this.instrumentIngestionService.ingest();
    if (this.isEngineRunning) return;
    this.logger.log('Historical ingestion job triggered.');
    await this.historicalIngestionService.ingestHistorical();
  }

  @Cron('* * * * *')
  async handleInterval() {
    const now = new Date();
    const minutes = now.getMinutes();
    const hours = now.getHours();
    const dayOfMonth = now.getDate();
    const dayOfWeek = now.getDay();
    const intervals = configurations('INTERVALS');
    const engineMode = configurations('ENGINE_MODE');

    if (engineMode === 'adapter') return;
    for (const interval of intervals) {
      try {
        switch (interval) {
          case 'M':
            if (minutes === 0 && hours === 0 && dayOfMonth === 1) {
              if (engineMode === 'worker') break;
              this.logger.log('Monthly job triggered.');
              this.logger.log('Historical ingestion job triggered.');
              await this.historicalIngestionService.ingestHistorical(interval);
              this.logger.log('Method update job triggered.');
              await this.methodStatusService.resetMethodStatus(interval);
            }
            break;
          case 'W':
            if (minutes === 0 && hours === 0 && dayOfWeek === 1) {
              this.logger.log('Weekly job triggered.');
              if (engineMode === 'worker') {
                this.logger.log('Historical ingestion job triggered.');
                await this.historicalIngestionService.ingestHistorical();
                break;
              }
              await this.historicalIngestionService.ingestHistorical(interval);
              await this.methodStatusService.resetMethodStatus(interval);
            }
            break;
          case 'D':
            if (minutes === 0 && hours === 0) {
              if (engineMode === 'worker') break;
              this.logger.log('Daily job triggered.');
              await this.historicalIngestionService.ingestHistorical(interval);
              await this.methodStatusService.resetMethodStatus(interval);
            }
            break;
          default:
            const intervalMinutes = parseInt(interval);
            if (isNaN(intervalMinutes)) {
              this.logger.warn(`Invalid interval: ${interval}`);
              continue;
            }

            if (minutes % 15 === 0) this.isEngineRunning = false;

            if (
              intervalMinutes >= 60 &&
              minutes === 0 &&
              hours % (intervalMinutes / 60) === 0
            ) {
              if (engineMode === 'worker') break;
              this.logger.log(
                `Interval ${intervalMinutes / 60}h reset triggered.`,
              );
              await this.historicalIngestionService.ingestHistorical(interval);
              await this.methodStatusService.resetMethodStatus(interval);
            } else if (
              intervalMinutes < 60 &&
              minutes % intervalMinutes === 0
            ) {
              if (engineMode === 'worker') break;
              const tradeExecutorEnabled = configurations(
                'TRADE_EXECUTOR_ENABLED',
              );
              if (this.isEngineRunning) return;
              this.isEngineRunning = true;
              this.logger.log(`Interval ${interval}m job triggered.`);
              await this.historicalIngestionService.ingestHistorical(interval);
              await this.methodStatusService.resetMethodStatus(interval);
              await this.methodStatusService.resetMethodStatusByRunningResult();
              await this.methodUpdaterService.updateMethod(now);
              await this.tradeResultUpdaterService.updateTradeResult();
              if (tradeExecutorEnabled) await this.traderExecutorService.run();
              this.isEngineRunning = false;
            }
            break;
        }
      } catch (err) {
        this.logger.error(`Error during reset for interval ${interval}:`, err);
      }
    }
  }
}
