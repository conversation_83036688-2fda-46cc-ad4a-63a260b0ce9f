import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { Historical } from 'src/interface/historical.interface';
import { logDetail } from 'src/util/log-detail.util';
import { Logger } from 'winston';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class HistoricalCacheService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  private getYearMonth(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    return `${year}_${month}`;
  }

  private getMonthlyFilePaths(symbol: string, interval: string, start?: Date, end?: Date): string[] {
    const basePath = path.join('data/historical', symbol, interval);
    
    if (!fs.existsSync(basePath)) {
      return [];
    }

    const files = fs.readdirSync(basePath)
      .filter(file => file.endsWith('.csv') && file.match(/^\d{4}_\d{2}\.csv$/))
      .sort();

    if (!start && !end) {
      return files.map(file => path.join(basePath, file));
    }

    const startYearMonth = start ? this.getYearMonth(start) : null;
    const endYearMonth = end ? this.getYearMonth(end) : null;

    return files
      .filter(file => {
        const yearMonth = file.replace('.csv', '');
        if (startYearMonth && yearMonth < startYearMonth) return false;
        if (endYearMonth && yearMonth > endYearMonth) return false;
        return true;
      })
      .map(file => path.join(basePath, file));
  }

  private async writeWithRetry(
    filePath: string,
    data: Historical[],
    retries = 3,
  ): Promise<void> {
    const tempFilePath = `${filePath}.tmp`;

    for (let i = 0; i < retries; i++) {
      try {
        const csvContent = this.convertToCsv(data);
        fs.writeFileSync(tempFilePath, csvContent, 'utf-8');
        fs.renameSync(tempFilePath, filePath);
        return;
      } catch (err) {
        if (i === retries - 1) throw err;
        await new Promise((resolve) => setTimeout(resolve, 100 * (i + 1)));
      } finally {
        if (fs.existsSync(tempFilePath)) {
          try {
            fs.unlinkSync(tempFilePath);
          } catch (cleanupErr) {
            this.logger.warn(
              'Failed to cleanup temp file',
              logDetail({
                class: 'HistoricalCacheService',
                function: 'writeWithRetry',
                error: cleanupErr,
                param: tempFilePath,
              }),
            );
          }
        }
      }
    }
  }

  private convertToCsv(data: Historical[]): string {
    if (!data.length)
      return 'date,symbol,interval,open,high,low,close,volume\n';

    const header = 'date,symbol,interval,open,high,low,close,volume\n';
    const rows = data
      .map(
        (item) =>
          `${new Date(item.date).toISOString()},${item.symbol},${item.interval},${item.open},${item.high},${item.low},${item.close},${item.volume}`,
      )
      .join('\n');

    return header + rows;
  }

  private parseCsvFile(filePath: string): Historical[] {
    if (!fs.existsSync(filePath)) {
      this.logger.debug(
        `CSV file does not exist: ${filePath}`,
        logDetail({
          class: 'HistoricalCacheService',
          function: 'parseCsvFile',
          param: { filePath },
        }),
      );
      return [];
    }

    try {
      const content = fs.readFileSync(filePath, 'utf-8').trim();
      if (!content) {
        this.logger.debug(
          `CSV file is empty: ${filePath}`,
          logDetail({
            class: 'HistoricalCacheService',
            function: 'parseCsvFile',
            param: { filePath },
          }),
        );
        return [];
      }

      const lines = content.split('\n');
      if (lines.length <= 1) {
        this.logger.debug(
          `CSV file has no data rows: ${filePath}`,
          logDetail({
            class: 'HistoricalCacheService',
            function: 'parseCsvFile',
            param: { filePath, lineCount: lines.length },
          }),
        );
        return [];
      }

      const validRecords: Historical[] = [];
      const invalidRecords: string[] = [];

      lines.slice(1).forEach((line, index) => {
        try {
          const [date, symbol, interval, open, high, low, close, volume] =
            line.split(',');

          if (
            !date ||
            !symbol ||
            !interval ||
            !open ||
            !high ||
            !low ||
            !close ||
            !volume
          ) {
            invalidRecords.push(`Line ${index + 2}: Missing fields`);
            return;
          }

          const parsedDate = new Date(date);
          if (isNaN(parsedDate.getTime())) {
            invalidRecords.push(`Line ${index + 2}: Invalid date format`);
            return;
          }

          const parsedOpen = parseFloat(open);
          const parsedHigh = parseFloat(high);
          const parsedLow = parseFloat(low);
          const parsedClose = parseFloat(close);
          const parsedVolume = parseFloat(volume);

          if (
            isNaN(parsedOpen) ||
            isNaN(parsedHigh) ||
            isNaN(parsedLow) ||
            isNaN(parsedClose) ||
            isNaN(parsedVolume)
          ) {
            invalidRecords.push(`Line ${index + 2}: Invalid numeric values`);
            return;
          }

          validRecords.push({
            date: parsedDate,
            symbol: symbol.trim(),
            interval: interval.trim(),
            open: parsedOpen,
            high: parsedHigh,
            low: parsedLow,
            close: parsedClose,
            volume: parsedVolume,
          });
        } catch (lineErr) {
          invalidRecords.push(
            `Line ${index + 2}: Parse error - ${lineErr.message}`,
          );
        }
      });

      if (invalidRecords.length > 0) {
        this.logger.warn(
          `Found ${invalidRecords.length} invalid records in ${filePath}`,
          logDetail({
            class: 'HistoricalCacheService',
            function: 'parseCsvFile',
            param: {
              filePath,
              totalLines: lines.length - 1,
              validRecords: validRecords.length,
              invalidRecords: invalidRecords.length,
              sampleErrors: invalidRecords.slice(0, 5), // Show first 5 errors
            },
          }),
        );
      }

      this.logger.debug(
        `Parsed CSV file: ${validRecords.length} valid records from ${filePath}`,
        logDetail({
          class: 'HistoricalCacheService',
          function: 'parseCsvFile',
          param: {
            filePath,
            validRecords: validRecords.length,
            invalidRecords: invalidRecords.length,
          },
        }),
      );

      return validRecords;
    } catch (err) {
      this.logger.error(
        `Failed to parse CSV file: ${filePath}`,
        logDetail({
          class: 'HistoricalCacheService',
          function: 'parseCsvFile',
          error: err,
          param: { filePath },
        }),
      );
      return [];
    }
  }

  async validateCache(symbol: string, interval: string, yearMonth?: string): Promise<boolean> {
    if (yearMonth) {
      const filePath = path.join('data/historical', symbol, interval, `${yearMonth}.csv`);
      if (!fs.existsSync(filePath)) return true;

      try {
        const data = this.parseCsvFile(filePath);
        if (!Array.isArray(data)) {
          await this.writeWithRetry(filePath, []);
          return false;
        }
        return true;
      } catch (err) {
        this.logger.warn(`Repairing corrupted cache file ${filePath}`);
        await this.writeWithRetry(filePath, []);
        return false;
      }
    } else {
      const filePaths = this.getMonthlyFilePaths(symbol, interval);
      let allValid = true;
      
      for (const filePath of filePaths) {
        try {
          const data = this.parseCsvFile(filePath);
          if (!Array.isArray(data)) {
            await this.writeWithRetry(filePath, []);
            allValid = false;
          }
        } catch (err) {
          this.logger.warn(`Repairing corrupted cache file ${filePath}`);
          await this.writeWithRetry(filePath, []);
          allValid = false;
        }
      }
      
      return allValid;
    }
  }

  async getHistorical(param: GetHistoricalDto): Promise<Historical[]> {
    const { symbol, interval, start, end, limit, sort } = param;

    if (!symbol || !interval) {
      this.logger.warn(
        'Missing required parameters for getHistorical',
        logDetail({
          class: 'HistoricalCacheService',
          function: 'getHistorical',
          param: { symbol, interval },
        }),
      );
      return [];
    }

    const filePaths = this.getMonthlyFilePaths(symbol, interval, start, end);
    
    if (filePaths.length === 0) {
      this.logger.debug(
        `No historical cache files found for ${symbol}-${interval}`,
        logDetail({
          class: 'HistoricalCacheService',
          function: 'getHistorical',
          param: { symbol, interval, start, end },
        }),
      );
      return [];
    }

    try {
      let allData: Historical[] = [];
      
      for (const filePath of filePaths) {
        if (fs.existsSync(filePath) && fs.statSync(filePath).size > 0) {
          const fileData = this.parseCsvFile(filePath);
          if (Array.isArray(fileData)) {
            allData.push(...fileData);
          }
        }
      }

      if (allData.length === 0) {
        this.logger.debug(
          `No data found in cache files for ${symbol}-${interval}`,
          logDetail({
            class: 'HistoricalCacheService',
            function: 'getHistorical',
            param: { symbol, interval, filesChecked: filePaths.length },
          }),
        );
        return [];
      }

      // Filter by start/end dates
      if (start) {
        allData = allData.filter((d) => d.date >= start);
      }
      if (end) {
        allData = allData.filter((d) => d.date <= end);
      }

      // Sort data
      allData.sort((a, b) =>
        sort === 'DESC'
          ? b.date.getTime() - a.date.getTime()
          : a.date.getTime() - b.date.getTime(),
      );

      // Apply limit
      if (limit && limit > 0) {
        allData = allData.slice(0, limit);
      }

      this.logger.debug(
        `Returning ${allData.length} historical records for ${symbol}-${interval}`,
        logDetail({
          class: 'HistoricalCacheService',
          function: 'getHistorical',
          param: { symbol, interval, finalCount: allData.length, filesUsed: filePaths.length },
        }),
      );

      return allData;
    } catch (err) {
      this.logger.error(
        'Failed to read historical data',
        logDetail({
          class: 'HistoricalCacheService',
          function: 'getHistorical',
          error: err,
          param: { ...param, filePaths },
        }),
      );
      return [];
    }
  }

  async insertHistorical(param: Historical[]): Promise<void> {
    if (!param.length) return;

    const symbol = param[0].symbol;
    const interval = param[0].interval;
    const dirPath = path.join('data/historical', symbol, interval);
    
    fs.mkdirSync(dirPath, { recursive: true });

    // Group data by year_month
    const monthlyData = new Map<string, Historical[]>();
    param.forEach(item => {
      const yearMonth = this.getYearMonth(item.date);
      if (!monthlyData.has(yearMonth)) {
        monthlyData.set(yearMonth, []);
      }
      monthlyData.get(yearMonth)!.push(item);
    });

    // Process each month's data
    for (const [yearMonth, data] of monthlyData) {
      const filePath = path.join(dirPath, `${yearMonth}.csv`);
      
      let existingData: Historical[] = [];
      if (fs.existsSync(filePath)) {
        existingData = this.parseCsvFile(filePath);
      }

      // Merge and deduplicate
      const map = new Map<string, Historical>();
      [...existingData, ...data].forEach((item) => {
        map.set(new Date(item.date).toISOString(), item);
      });

      const merged = Array.from(map.values()).sort(
        (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
      );

      await this.writeWithRetry(filePath, merged);
    }
  }

  async deleteHistorical(param: {
    symbol: string;
    interval: string;
    yearMonth?: string;
  }): Promise<void> {
    if (param.yearMonth) {
      // Delete specific month file
      const filePath = path.join('data/historical', param.symbol, param.interval, `${param.yearMonth}.csv`);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } else {
      // Delete entire interval directory
      const dirPath = path.join('data/historical', param.symbol, param.interval);
      if (fs.existsSync(dirPath)) {
        fs.rmSync(dirPath, { recursive: true, force: true });
      }
    }
  }

  async countHistorical(
    symbol: string,
    interval: string,
    start?: Date,
    end?: Date,
  ): Promise<number> {
    if (!symbol || !interval) {
      this.logger.warn(
        'Missing required parameters for countHistorical',
        logDetail({
          class: 'HistoricalCacheService',
          function: 'countHistorical',
          param: { symbol, interval },
        }),
      );
      return 0;
    }

    const filePaths = this.getMonthlyFilePaths(symbol, interval, start, end);
    
    if (filePaths.length === 0) {
      return 0;
    }

    try {
      let totalCount = 0;
      
      for (const filePath of filePaths) {
        if (fs.existsSync(filePath) && fs.statSync(filePath).size > 0) {
          let data = this.parseCsvFile(filePath);
          
          if (Array.isArray(data)) {
            // Filter by start/end dates
            if (start) {
              data = data.filter((d) => d.date >= start);
            }
            if (end) {
              data = data.filter((d) => d.date <= end);
            }
            totalCount += data.length;
          }
        }
      }

      this.logger.debug(
        `Total count for ${symbol}-${interval}: ${totalCount}`,
        logDetail({
          class: 'HistoricalCacheService',
          function: 'countHistorical',
          param: { symbol, interval, totalCount, filesChecked: filePaths.length },
        }),
      );

      return totalCount;
    } catch (err) {
      this.logger.error(
        'Failed to count historical cache',
        logDetail({
          class: 'HistoricalCacheService',
          function: 'countHistorical',
          error: err,
          param: { symbol, interval, start, end, filePaths },
        }),
      );
      return 0;
    }
  }

  async getAvailableMonths(symbol: string, interval: string): Promise<string[]> {
    const dirPath = path.join('data/historical', symbol, interval);
    
    if (!fs.existsSync(dirPath)) {
      return [];
    }

    return fs.readdirSync(dirPath)
      .filter(file => file.endsWith('.csv') && file.match(/^\d{4}_\d{2}\.csv$/))
      .map(file => file.replace('.csv', ''))
      .sort();
  }

  async getCacheInfo(symbol: string, interval: string): Promise<{
    totalMonths: number;
    months: string[];
    totalRecords: number;
    oldestRecord?: Date;
    newestRecord?: Date;
  }> {
    const months = await this.getAvailableMonths(symbol, interval);
    
    if (months.length === 0) {
      return {
        totalMonths: 0,
        months: [],
        totalRecords: 0
      };
    }

    let totalRecords = 0;
    let oldestRecord: Date | undefined;
    let newestRecord: Date | undefined;

    for (const month of months) {
      const filePath = path.join('data/historical', symbol, interval, `${month}.csv`);
      if (fs.existsSync(filePath)) {
        const data = this.parseCsvFile(filePath);
        if (Array.isArray(data) && data.length > 0) {
          totalRecords += data.length;
          
          const sortedData = data.sort((a, b) => a.date.getTime() - b.date.getTime());
          const firstDate = sortedData[0].date;
          const lastDate = sortedData[sortedData.length - 1].date;
          
          if (!oldestRecord || firstDate < oldestRecord) {
            oldestRecord = firstDate;
          }
          if (!newestRecord || lastDate > newestRecord) {
            newestRecord = lastDate;
          }
        }
      }
    }

    return {
      totalMonths: months.length,
      months,
      totalRecords,
      oldestRecord,
      newestRecord
    };
  }

  async cleanupEmptyMonths(symbol: string, interval: string): Promise<number> {
    const months = await this.getAvailableMonths(symbol, interval);
    let cleanedCount = 0;

    for (const month of months) {
      const filePath = path.join('data/historical', symbol, interval, `${month}.csv`);
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        if (stats.size === 0) {
          fs.unlinkSync(filePath);
          cleanedCount++;
          this.logger.debug(`Cleaned up empty month file: ${filePath}`);
        } else {
          const data = this.parseCsvFile(filePath);
          if (!Array.isArray(data) || data.length === 0) {
            fs.unlinkSync(filePath);
            cleanedCount++;
            this.logger.debug(`Cleaned up empty month file: ${filePath}`);
          }
        }
      }
    }

    return cleanedCount;
  }

  async cleanupOldBackups(maxAgeDays = 7): Promise<void> {
    const now = Date.now();
    const maxAgeMs = maxAgeDays * 24 * 60 * 60 * 1000;

    if (!fs.existsSync('data/historical')) return;

    const symbolDirs = fs.readdirSync('data/historical');
    for (const symbolDir of symbolDirs) {
      const symbolPath = path.join('data/historical', symbolDir);
      
      if (!fs.statSync(symbolPath).isDirectory()) continue;
      
      const intervalDirs = fs.readdirSync(symbolPath);
      for (const intervalDir of intervalDirs) {
        const intervalPath = path.join(symbolPath, intervalDir);
        
        if (!fs.statSync(intervalPath).isDirectory()) continue;
        
        const files = fs.readdirSync(intervalPath);
        for (const file of files) {
          if (file.includes('.corrupted.') || file.includes('.backup.')) {
            const filePath = path.join(intervalPath, file);
            try {
              const stats = fs.statSync(filePath);
              if (now - stats.mtimeMs > maxAgeMs) {
                fs.unlinkSync(filePath);
                this.logger.debug(`Cleaned up old backup: ${filePath}`);
              }
            } catch (err) {
              this.logger.warn(
                `Failed to cleanup backup ${filePath}`,
                logDetail({ error: err }),
              );
            }
          }
        }
      }
    }
  }
}
